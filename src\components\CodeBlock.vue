<template>
  <div class="code-container" :class="{ collapsed: isCollapsed && shouldCollapse }">
    <div class="code-header">
      <span class="language-tag" >{{ language }}</span>
    </div>
    <pre ref="codePre" :class="['code-content', { 'has-collapse': shouldCollapse }]">
      <div
        v-html="code"
      />
    </pre>
    <div v-if="shouldCollapse" class="collapse-control">
      <button @click="toggleCollapse" class="collapse-btn">
        <span v-if="isCollapsed"
          ><el-icon><ArrowDown /></el-icon
        ></span>
        <span v-else
          ><el-icon><ArrowUp /></el-icon
        ></span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

const props = defineProps({
  code: String,
  // language: String
  classes: String
});

const language = ref<string>('');
const isCollapsed = ref(false);
const codePre = ref(null);
const lineCount = ref(0);
const shouldCollapse = computed(() => lineCount.value > 7);

onMounted(() => {
  // 计算代码行数
  lineCount.value = props.code.split('\n').length;
  if (shouldCollapse.value) isCollapsed.value = true;
});

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
watch(
  () => props.code,
  (newVal) => {
    console.log('trycode', newVal);
  }
);

watch(
  () => props.classes,
  (newVal: string | undefined | null) => {
    if (newVal) {
      const array = newVal.split(' ');
      for (const item of array) {
        // 检查是否以'language'开头的标准格式
        if (item.startsWith('language')) {
          const myLanguage = item.split('-')[1];
          language.value = myLanguage;
          // console.log('mylanguage', language.value);
          break;
        }
        // 检查是否是直接的语言名称（不包含hljs等关键词）
        else if (item && item !== 'hljs' && !item.includes('-') && /^[a-zA-Z]+$/.test(item)) {
          language.value = item;
          // console.log('mylanguage', language.value);
          break;
        }
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
/* 整体容器 */
.code-container {
  position: relative;
  margin: 1em 0;
  //background: #dcdcdc;
  border-radius: 4px;
  overflow-y: hidden;
  overflow-x: auto; /* 添加水平滚动 */
  transition: all 0.3s ease;
  background-color: #221f22 !important;
  max-width: 100%; /* 限制最大宽度为父容器的100% */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
  
  .code-header {
    box-shadow: 0px 4px 5px -6px #888888;
    margin-bottom: 1px;
    min-height: 0;
    padding: 8px 16px;
    .language-tag {
      color: #abb2bf;
      font-size: 0.9em;
      font-family: 'Consolas', monospace;
    }
  }

  .code-content {
    padding: 15px;
    padding-top: 0;
    padding-bottom: 0;
    background-color: #221f22;
    color: #f8f8f8;
    font-family: 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
    transition: max-height 0.3s ease;
    & > div {
      margin-top: -15px;
    }
  }

  .collapse-control {
    display: flex;
    justify-content: center;
    padding: 2px;
    background: #221f22;

    .collapse-btn {
      background: none;
      border: none;
      color: #808080;
      cursor: pointer;
      padding: 4px 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: color 0.2s;
      font-size: 26px;
    }

    .collapse-btn:hover {
      color: #0d29a5;
    }
  }
}

/* 折叠状态 */
.code-container.collapsed .code-content {
  max-height: 200px;
  overflow: hidden;
}

/* 代码行号样式*/
.hljs {
  counter-reset: line;
}

.hljs .line::before {
  counter-increment: line;
  content: counter(line);
  display: inline-block;
  width: 2em;
  padding-right: 1em;
  color: #858585;
  text-align: right;
  user-select: none;
}
</style>
