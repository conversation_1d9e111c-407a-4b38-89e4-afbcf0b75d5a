<template>
  <div ref="warpper" class="warpper textfont">
    <!-- 项目信息卡片 -->
    <div class="progress-card">
      <!-- 卡片头部 - 始终显示 -->
      <div class="card-header">
        <div class="header-left">
          <div class="project-info">
            <div>
              <img class="backBtn" @click="handleBack()" src="@/assets/images/prjlearn/u4508.svg" />
              <span class="titlefont title" v-html="prjInfo.title"></span>
            </div>
            <div class="meta-info">
              <div class="author-info">
                <img :src="prjInfo.userCoverPic" class="avatar" />
                <span class="author-name">{{ prjInfo.userName }}</span>
              </div>
              <span class="create-time">{{ prjInfo.createTime }}</span>
              <el-icon
                class="expand-arrow"
                :class="{ expanded: progressExpanded }"
                @click="toggleProgressExpand"
              >
                <ArrowDown />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 可折叠的进度内容 -->
      <el-collapse-transition>
        <div v-show="progressExpanded" class="progress-content textfont">
          <!-- 进度圆环区域 -->
          <div class="progress-circles">
            <div class="progress-item">
              <el-progress
                type="circle"
                :percentage="graspKlgPct"
                :width="56"
                :stroke-width="3"
                color="#67c23a"
              />
              <div class="progress-label">
                <span class="label-text"
                  >已掌握{{ klg.masteredKlgCount || 0 }}/{{ klg.klgCount || 0 }}</span
                >
              </div>
            </div>
            <div class="progress-item">
              <el-progress
                type="circle"
                :percentage="fullyGraspKlgPct"
                :width="56"
                :stroke-width="3"
                color="#67c23a"
              />
              <div class="progress-label">
                <span class="label-text textfont"
                  >全掌握{{ klg.fullyMasteredKlgCount || 0 }}/{{ klg.klgCount || 0 }}
                </span>
              </div>
            </div>
          </div>

          <!-- 标签区域 -->
          <div class="tags-section">
            <div class="tags-grid">
              <div v-for="tag in displayTags" :key="tag.id" class="tag-item">
                {{ tag.content }}
              </div>
              <div v-if="tags.length > 4" class="tag-item more-tag" @click="handleExpandTag">
                <el-icon><More /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <div class="divider"></div>

    <!-- 章节列表 -->
    <div class="chapter-section hover-scrollbar">
      <div class="section-header">
        <span class="titlefont chapterCatalog">项目章节</span>
        <!-- 问题筛选替代章节计数的位置 -->
        <div class="question-filter">
          <el-checkbox v-model="checkMyQuestion" size="large" @change="handleClickCheckBox">
            只看我的提问
          </el-checkbox>
        </div>
      </div>

      <div class="chapter-list" v-if="chapterList && chapterList.length > 0">
        <div v-for="(chapter, index) in chapterList" :key="chapter.chapterId" class="chapter-item">
          <div class="chapter-content">
            <div
              class="chapter-header"
              @click="handleChapterChange(chapter.chapterId)"
              :class="{
                active: activeChapterId == chapter.chapterId
              }"
            >
              <div class="chapter-name" :title="chapter.chapterName">
                {{ `${chapter.chapterName}` }}
              </div>
              <el-icon
                class="chapter-expand-arrow"
                :class="{ expanded: isChapterExpanded(chapter.chapterId) }"
                @click.stop="toggleChapterExpand(chapter.chapterId)"
                v-if="questionList[index] && questionList[index].length > 0"
              >
                <ArrowDown />
              </el-icon>
            </div>
          </div>

          <!-- 章节下的问题列表 -->
          <div
            class="question-list"
            v-if="
              questionList[index] &&
              questionList[index].length > 0 &&
              isChapterExpanded(chapter.chapterId)
            "
          >
            <div
              v-for="question in questionList[index]"
              :key="question.questionId"
              class="question-item"
              :class="{ active: question.questionId == curQuestionId }"
              @click="changeQuestion(question.questionId, chapter.chapterId)"
            >
              <div class="question-content" :class="{ learned: question.learned }">
                <template v-if="question.questionType != '开放性问题'">
                  <div
                    class="keyWords ellipsis"
                    v-html="'【' + question.keyword + '】' + question.questionType + '?'"
                  ></div>
                </template>
                <template v-else>
                  <div class="keyWords ellipsis" v-html="'【' + question.keyword + '】'"></div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-chapters">暂无章节内容</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPrjMoreInfoApi } from '@/apis/learning';
import { getQuestionListApi } from '@/apis/case';
import type { PrjinfoItf, PrjTag as PrjTagType, Chapter } from '@/types/learning';
import { PrjType } from '@/types/project';
import { useRouter } from 'vue-router';
import {
  Lock,
  VideoPlay,
  ArrowDown,
  ArrowLeft,
  ArrowUp,
  More,
  InfoFilled
} from '@element-plus/icons-vue';
import nzhcn from 'nzh/cn';
import type { CheckboxValueType } from 'element-plus';
import {
  QuestionNavigator,
  type QuestionItem,
  type NavigationResult
} from '@/utils/questionNavigator';

// 定义emits
const emit = defineEmits<{
  questionChange: [questionId: string, chapterId: string];
  chapterChange: [chapterId: string];
  navigateAfterDelete: [result: NavigationResult];
}>();

const prjInfo = inject('prjInfo') as Ref<PrjinfoItf>;
const route = useRoute();
const router = useRouter();

// 问题导航器实例
const questionNavigator = new QuestionNavigator();

const spuId = route.query.spuId as string;
const targetKlgs = ref<any>([]);
const klg = reactive({
  klgCount: 0,
  masteredKlgCount: 0,
  fullyMasteredKlgCount: 0
});
const graspKlgPct = computed(() => {
  if (!klg.klgCount) return 0;
  return Math.floor((klg.masteredKlgCount / klg.klgCount) * 100);
});
const fullyGraspKlgPct = computed(() => {
  if (!klg.klgCount) return 0;
  return Math.floor((klg.fullyMasteredKlgCount / klg.klgCount) * 100);
});
const warpper = ref();
const tags = prjInfo.value.prjTags as Array<PrjTagType>;

// 章节相关数据
const chapterList = ref<Chapter[]>([]);
const activeChapterId = ref<string | number>();
const curQuestionId = ref<string | number>();

// 问题相关数据
const questionList = ref<any[]>([]);
const checkMyQuestion = ref(false);

// 进度卡片相关数据
const progressExpanded = ref(false);
const tagsVisible = ref(false);

// 章节展开状态管理 - 改为单个章节展开
const expandedChapter = ref<string | number | null>(null);

// 计算显示的标签
const displayTags = computed(() => {
  return tagsVisible.value ? tags : tags.slice(0, 4);
});

function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
}

// 切换进度卡片展开状态
const toggleProgressExpand = () => {
  progressExpanded.value = !progressExpanded.value;
};

// 切换章节展开状态 - 手风琴式单章节展开
const toggleChapterExpand = (chapterId: string | number) => {
  // 统一转换为字符串进行比较，避免类型不匹配
  if (String(expandedChapter.value) === String(chapterId)) {
    // 如果当前章节已展开，则折叠
    expandedChapter.value = null;
  } else {
    // 展开新章节，自动折叠其他章节
    expandedChapter.value = chapterId;
  }
};

// 检查章节是否展开
const isChapterExpanded = (chapterId: string | number) => {
  // 统一转换为字符串进行比较，避免类型不匹配
  const isExpanded = String(expandedChapter.value) === String(chapterId);
  return isExpanded;
};

// 处理返回
const handleBack = () => {
  router.push({
    path: '/goodIntroduce',
    query: { spuId: spuId, tab: 'questionList' }
  });
};

// 处理章节切换
const handleChapterChange = (chapterId: string | number) => {
  if (activeChapterId.value !== chapterId) {
    activeChapterId.value = chapterId;
    emit('chapterChange', String(chapterId));
  }
};

// 处理问题切换
const changeQuestion = (questionId: string | number, chapterId: string | number) => {
  if (questionId && questionId !== curQuestionId.value) {
    curQuestionId.value = questionId;
    activeChapterId.value = chapterId;
    emit('questionChange', String(questionId), String(chapterId));
  }
};

// 处理问题筛选
const handleClickCheckBox = async (value: CheckboxValueType) => {
  questionList.value = [];

  for (const chapter of chapterList.value) {
    const res = await getQuestionListApi({
      spuId,
      chapterId: chapter.chapterId,
      keyword: '',
      self: checkMyQuestion.value,
      hideLearned: false
    });
    questionList.value.push(res.data);
  }
};

// 初始化数据
const initData = async () => {
  try {
    // 获取项目详细信息
    const res = await getPrjMoreInfoApi(spuId);
    if (res && res.data) {
      const data = res.data as any;
      targetKlgs.value = data.targetKlgs || [];

      // 正确更新响应式对象的属性
      if (data.klg) {
        Object.assign(klg, {
          klgCount: data.klg.klgCount || 0,
          masteredKlgCount: data.klg.masteredKlgCount || 0,
          fullyMasteredKlgCount: data.klg.fullyMasteredKlgCount || 0
        });
      }
    }
  } catch (error) {
    // 静默处理错误
  }
};

// 设置章节和问题数据
const setChapterAndQuestionData = async (
  chapters: Chapter[],
  questions: any[],
  currentChapterId?: string,
  currentQuestionId?: string
) => {
  chapterList.value = chapters;
  activeChapterId.value = currentChapterId;
  curQuestionId.value = currentQuestionId;

  // 先重置章节展开状态
  expandedChapter.value = null;

  // 获取所有章节的问题列表
  questionList.value = [];
  for (const chapter of chapters) {
    try {
      const res = await getQuestionListApi({
        spuId,
        chapterId: chapter.chapterId,
        keyword: '',
        self: checkMyQuestion.value,
        hideLearned: false
      });
      questionList.value.push(res.data || []);
    } catch (error) {
      // 静默处理错误
      questionList.value.push([]);
    }
  }

  // 初始化问题导航器
  questionNavigator.buildNavigationData(chapters, questionList.value);

  // 问题列表加载完成后，处理章节展开逻辑
  if (currentQuestionId && currentChapterId) {
    // 如果有当前问题ID，展开该问题所在的章节
    expandedChapter.value = currentChapterId;
    console.log('✅ 自动展开问题所在章节:', currentChapterId, '问题ID:', currentQuestionId);
  } else if (questionList.value.length > 0) {
    // 如果没有指定问题ID，但有问题列表，选择第一个问题并展开其所在章节
    for (let i = 0; i < questionList.value.length; i++) {
      const questions = questionList.value[i];
      if (questions.length > 0) {
        const firstQuestion = questions[0];
        const firstQuestionChapterId = chapterList.value[i]?.chapterId || currentChapterId;
        // 展开第一个问题所在的章节
        if (firstQuestionChapterId) {
          expandedChapter.value = firstQuestionChapterId;
          console.log('自动展开第一个有问题的章节:', firstQuestionChapterId);
          emit('questionChange', String(firstQuestion.questionId), String(firstQuestionChapterId));
        }
        break;
      }
    }
  }
};

// 刷新问题列表
const refreshQuestionList = async () => {
  // 重新获取所有章节的问题列表
  questionList.value = [];
  for (const chapter of chapterList.value) {
    try {
      const res = await getQuestionListApi({
        spuId,
        chapterId: chapter.chapterId,
        keyword: '',
        self: checkMyQuestion.value,
        hideLearned: false
      });
      questionList.value.push(res.data || []);
    } catch (error) {
      // 静默处理错误
      questionList.value.push([]);
    }
  }

  // 重新初始化问题导航器
  questionNavigator.buildNavigationData(chapterList.value, questionList.value);
};

// 处理删除问题后的导航
const handleQuestionDeleted = (questionId: string, chapterId: string): NavigationResult => {
  // 先获取删除后的下一个问题（在删除前计算）
  const navigationResult = questionNavigator.getNextQuestionAfterDelete(questionId, chapterId);

  // 然后从导航器中移除问题
  const removed = questionNavigator.removeQuestion(questionId, chapterId);

  if (!removed) {
    return { type: 'project-detail' };
  }

  // 检查剩余问题数量
  const remainingCount = questionNavigator.getTotalQuestionCount();

  // 如果没有剩余问题，返回项目详情页
  if (remainingCount === 0) {
    return { type: 'project-detail' };
  }

  return navigationResult;
};

// 暴露方法给父组件
defineExpose({
  setChapterAndQuestionData,
  refreshQuestionList,
  handleQuestionDeleted
});

onMounted(() => {
  initData();
});
</script>

<style scoped lang="less">
.titlefont {
  font-size: 18px;
}
.title {
  display: inline-block;
}
.warpper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.996078431372549);
  padding: 10px;

  overflow-x: hidden;
  width: 345px;
}

// 进度卡片样式
.progress-card {
  flex-shrink: 0;
  overflow: hidden;
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
    }

    .header-left {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;

      .project-info {
        flex: 1;
        min-width: 0;

        .meta-info {
          margin-left: 25px;
          margin-top: 5px;
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 11px;
          position: relative;
          .author-info {
            display: flex;
            align-items: center;
            gap: 4px;

            .avatar {
              width: 16px;
              height: 16px;
              border-radius: 50%;
            }

            .author-name {
              max-width: 80px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 12px;
            }
          }

          .create-time {
            color: #666666;
            font-size: 12px;
          }
        }
      }
    }

    .expand-arrow {
      transition: transform 0.2s ease;
      position: absolute;
      right: 5px;
      font-size: 15px;
      cursor: pointer;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .progress-content {
    margin-top: 20px;
    padding: 0 40px 0;
    .progress-circles {
      display: flex;
      justify-content: space-around;
      margin-bottom: 20px;

      .progress-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .progress-label {
          margin-top: 8px;
          text-align: center;

          .label-text {
            margin-bottom: 2px;
          }
        }
      }
    }

    .tags-section {
      margin-bottom: 16px;

      .tags-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;

        .tag-item {
          padding: 6px 12px;
          background-color: #f0f2f5;
          border-radius: 16px;
          font-size: 11px;
          color: #333333;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          transition: all 0.2s ease;

          &:hover {
            background-color: #e6f7ff;
            color: #1890ff;
          }

          &.more-tag {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background-color: #409eff;
              color: white;
            }
          }
        }
      }
    }
  }
}
.backBtn {
  cursor: pointer;
  height: 14px;
  width: 15px;
  margin-right: 10px;
}

.chapter-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 22px;
  overflow-y: auto;
  overflow-x: hidden;
  // 章节区域
  .chapterCatalog {
    font-size: 14px !important;
  }
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    // 自定义复选框样式
    :deep(.el-checkbox) {
      .el-checkbox__input {
        .el-checkbox__inner {
          &:hover {
            border-color: #1973cb;
          }
        }

        &.is-checked {
          .el-checkbox__inner {
            background-color: #1973cb;
            border-color: #1973cb;
          }

          .el-checkbox__label {
            color: #1973cb !important;
          }
        }
      }

      .el-checkbox__label {
        &:hover {
          color: #1973cb;
        }
      }

      // 确保选中时文字颜色为 #1973cb
      &.is-checked {
        .el-checkbox__label {
          color: #1973cb !important;
        }
      }
    }

    .chapter-count {
      font-size: 12px;
      color: #666;
    }
  }

  .chapter-item {
    margin-bottom: 15px;

    .chapter-content {
      margin-bottom: 8px;

      .chapter-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          background-color: #f2f2f2;
          scale: 1.05;
        }
        &.active {
          background-color: #f2f2f2;
        }

        .chapter-name {
          font-size: 13px;
          font-weight: 600;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
        }

        .chapter-expand-arrow {
          transition: transform 0.2s ease;
          font-size: 14px;
          cursor: pointer;
          color: #666;
          margin-left: 8px;
          flex-shrink: 0;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }

    .question-list {
      padding-left: 10px;
      width: 100%;
    }

    .question-item {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      margin-bottom: 5px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      width: 100%;

      &:hover {
        background-color: #f2f2f2;
        scale: 1.05;
      }

      &.active {
        background-color: #f2f2f2;
      }

      .question-content {
        max-height: 100%;
        line-height: 1;
        width: 100%;
      }
    }
  }

  .no-chapters {
    text-align: center;
    color: #999;
    font-size: 12px;
    padding: 40px 20px;
  }
}

.divider {
  flex-shrink: 0; /* 防止被压缩 */
  height: 2px;
  width: 300px;
  background-color: #f0f0f0;
  margin-left: 25px;
  margin-top: 5px;
  margin-bottom: 10px;
}
</style>
