<template>
  <div class="main-wrapper" :class="{ big: isBig, map: isMap }">
    <div class="main">
      <div class="video-title">
        <div class="titlefont">
          {{ projectDetailData?.chapterName }}
        </div>
        <div class="klg-icon">
          <div class="klg-tooltip">知识源图</div>
          <span class="klgbtn klg-icon-circle" size="16">
            <img src="@/assets/svgs/kGraph.svg" @click="changeView" alt="" srcset="" />
          </span>
        </div>
      </div>
      <el-divider class="divider" />

      <div>
        <template v-if="wordContent">
          <!-- {{ wordContent }} -->
          <PrjManuscript
            :renderContent="renderContent"
            :questionList="questionList ?? []"
            @refresh="handleQuestionList"
            ref="manuScript"
            @close-map="closeMap"
            class="lineWordContent manuscript-container"
            :buyStatus="buyStatus"
            @update:payDialogVisible="handleUpdatePayDialogVisible"
          ></PrjManuscript>
        </template>
      </div>
    </div>

    <!-- 问号图标 -->
    <div
      v-if="questionIconVisible"
      ref="questionIconElement"
      class="question-icon"
      :style="{
        position: 'fixed',
        left: questionIconPosition.x + 'px',
        top: questionIconPosition.y + 'px',
        zIndex: 10000
      }"
      @click="handleQuestionIconClick"
    >
      <!-- 悬浮提示 -->
      <div class="question-tooltip">提问</div>
      <!-- 问号图标 -->
      <div class="question-icon-circle">
        <img :src="questionIcon" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  'update:payDialogVisible1': [value: boolean];
}>();

// 处理显示PayDialog
const handleUpdatePayDialogVisible = (value: boolean) => {
  console.log('PrjManuWrapper 到这里了');
  // 通过emit通知父组件打开PayDialog
  emit('update:payDialogVisible1', true);
};

import {
  getManuProjectSectionApi,
  getQuestionListApi,
  deleteQuestionApi,
  saveQuestionApi
} from '@/apis/learning';
import type { Chapter, QuestionData } from '@/types/learning';
import { STATE_FLAG } from '@/types/learning';
import PrjManuscript from './PrjManuscript.vue';
import { defineExpose } from 'vue';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { useLearningStore } from '@/stores/learning';
import { emitter } from '@/utils/emitter';
// import hljs from 'highlight.js';
import { Event } from '@/types/event';
import { getPrjIntroduceApi } from '@/apis/case';
import { useRenderManager } from '@/composables/useRenderManager';
import questionIcon from '@/assets/svgs/question.svg';
import { useQuestionIcon } from '@/composables/useQuestionIcon';

const hasPermission = ref(0);
const prjType = ref<number | undefined>(0);

// 注入购买状态，用于响应购买成功后的权限变化
const buyStatus = inject('buyStatus') as Ref<boolean>;
const learningStore = useLearningStore();

// const changedContent=ref()

const isSectionMenuShow = ref(false);
const manuScript = ref<InstanceType<typeof PrjManuscript>>();

// 项目的spuId
const route = useRoute();
const spuId = route.query.spuId as string;

const wordContent = ref();
const isMap = ref<boolean>(false); // 是否展示地图
const isBig = ref<boolean>(true); // 大屏小屏
// 项目的prjId
const prjId = ref();
const uniqueCode = ref();
// 项目的章节id
const curChapterId = ref();
// 项目详情
const projectDetailData = shallowRef<Chapter>();
// 章节相关的数据
const chapterList = shallowRef<Chapter[]>();
const activeIndex = ref();
// 拿到章节信息
const questionList = shallowRef<QuestionData[]>();
//内容id
const contentId = ref<string | number>();
const props = defineProps<{ payDialogVisible: boolean }>();

const payDialogVisible = ref(false);
//点击知识节点地图进行参数样式改变
const changeView = () => {
  isBig.value = !isBig.value;
  isMap.value = !isMap.value;
};

//关闭图标
const closeMap = () => {
  isBig.value = false;
  isMap.value = false;
};
// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon();
// 使用Render管理器
const renderContent = ref('');
const { initializeRender, reinitializeRender, addQuestion, removeQuestion } = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => wordContent.value,
  questionList,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    } else {
      console.log('❌ 选中文本为空或无效');
    }
  },
  onClick: (data: any) => {
    const event = new CustomEvent('showAnswerDrawer', {
      detail: { questionData: data.target }
    });
    window.dispatchEvent(event);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    // console.log('myonFinish', content);
    // console.log('wordContent1', wordContent.value);
    // setTimeout(() => {
    renderContent.value = content;
    // }, 1000);
    // console.log('wordContent2', wordContent.value);
  }
  // enableDebugLog: true
});
const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  const res = await getQuestionListApi(uniqueCode, chapterId);
  console.log('getQuestionListApi', res);
  questionList.value = res.data;
};

const removeQuestionFn = async ([questionId, associatedWords]: any) => {
  console.log('removeQuestionFn', questionId);
  const res = await deleteQuestionApi(questionId);
  // console.log("deleteQuestionApi",res.data)
  if (res.success) {
    ElMessage.success('删除成功');
    // 使用Render管理器处理问题删除
    removeQuestion(associatedWords, Number(questionId));
  } else {
    ElMessage.error('删除失败');
  }
};
const addQuestionFn = async (params: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) => {
  const res = await saveQuestionApi(params);
  const data = res.data.data;
  if (res.success) {
    ElMessage.success('保存问题成功');
    console.log('保存的问题:', data);
    addQuestion(data.associatedWords, data.questionId);
  } else {
    const errorMsg = res.message || '保存问题失败，请重试';
    ElMessage.error(errorMsg);
  }
};

watch(
  () => props.payDialogVisible,
  (newVal) => {
    if (newVal == false) {
      payDialogVisible.value = false;
    }
  }
);

// 监听isMap变化，当地图关闭后重新初始化Render
watch(
  () => isMap.value,
  async (newVal, oldVal) => {
    // 当从显示地图切换到隐藏地图时，重新初始化Render
    if (oldVal === true && newVal === false) {
      console.log('地图已关闭，准备重新初始化Render...');
      // 等待DOM更新完成后再重新初始化
      await nextTick();
      await reinitializeRender();
    }
  }
);

// 处理章节变化
//当我切换到当前章节时，我需要发送老的end请求和当前章节的start请求, 结束老章节的start请求和当前章节的end请求

const { initUserBehaviour, handleChapterChange } = userBehaviour(
  PrjForm.draft,
  activeIndex,
  curChapterId
);

import type { QuestionType } from '@/types/question';

const handleChangeSectionFn = async (chapterId: number) => {
  if (activeIndex.value != chapterId) {
    console.log('🔄 章节切换开始 - 保持旧内容显示');

    // 🔥 关键优化：不立即清空数据，保持旧内容显示
    handleChapterChange(chapterId);

    // 🔥 关闭打开的抽屉组件
    if (manuScript.value) {
      manuScript.value.handleCloseQuestionDrawer();
      manuScript.value.handleCloseAnswerDrawer();
    }

    try {
      // 先获取新章节数据
      const res = await getManuProjectSectionApi(spuId, String(chapterId));

      // 获取新章节的问题列表
      const questionRes = await getQuestionListApi(uniqueCode.value, String(res.data.chapterId));
      // 更新章节相关数据
      activeIndex.value = chapterId;
      projectDetailData.value = res.data as any;
      curChapterId.value = res.data.chapterId;
      contentId.value = res.data.contentId;
      learningStore.contentId = String(contentId.value);
      learningStore.chapterId = String(res.data.chapterId);

      // 更新内容数据
      wordContent.value = res.data.wordContent;
      questionList.value = questionRes.data as any;

      console.log('✅ 数据批量更新完成');

      // 等待DOM更新后重新初始化Render实例
      await nextTick();
      console.log('🚀 重新初始化Render实例');
      await reinitializeRender();
      console.log('🎉 章节切换完成');
    } catch (error) {
      console.error('❌ 章节切换失败:', error);
      // 发生错误时恢复原状态
      activeIndex.value = activeIndex.value; // 保持原来的章节
    }
  }
  isSectionMenuShow.value = false;
};
const assessmentId = ref();

//FIXME：7.20
//目前方案：通过记录beforeunload的时间戳和unload的时间戳之差， 大于10是刷新，小于10是关闭，这个值本身可能依赖于页面复杂度和电脑性能
//不是特别稳定，有时候关闭会被判断为刷新，基本准确率能达到90%以上
//也无法实现关掉一个网页然后又快速打开的防抖操作
//断网了就没有历史记录了，没办法，因为发送endStudy请求要网qwq

//7.22
//现在不对刷新和关闭做区分了, 只对change可见性的操作做防抖

// 监听路由变化
watch(
  () => route.query.chapterId,
  async (newChapterId) => {
    if (newChapterId && Number(newChapterId) !== activeIndex.value) {
      console.log('路由章节变化:', newChapterId);
      await handleChangeSectionFn(Number(newChapterId));
    }
  }
);

onMounted(async () => {
  let idx = learningStore.validIndex;
  projectDetailData.value = learningStore.chapterList[idx]; // 默认第一个
  assessmentId.value = projectDetailData.value?.assessmentId; // assessmentId
  chapterList.value = learningStore.chapterList; // 拿到章节列表
  curChapterId.value = projectDetailData.value?.chapterId;
  activeIndex.value = projectDetailData.value?.chapterId;
  // questionList.value = prjDetailData.value?.questionList; // 拿到问题信息
  // console.log('questionList', questionList.value);
  // console.log('prjDetailData.value?.wordContent', prjDetailData.value?.wordContent)
  wordContent.value = learningStore.chapterList[idx].wordContent; //拿到富文本内容
  // console.log('wordContent', wordContent.value);

  // console.log('wordContent.value', wordContent.value);
  uniqueCode.value = learningStore.uniqueCode;
  prjId.value = learningStore.prjId; // 拿到项目id
  contentId.value = projectDetailData.value?.contentId;
  learningStore.contentId = contentId.value;
  initUserBehaviour(curChapterId.value);
  await handleQuestionList(uniqueCode.value, curChapterId.value);
  await nextTick();
  await initializeRender();
});

onMounted(() => {
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  // 添加全局点击事件监听 - 使用捕获阶段
  document.addEventListener('click', handleDocumentClick as EventListener, true);
});

onBeforeUnmount(() => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);

  // 移除全局点击事件监听
  document.removeEventListener('click', handleDocumentClick as EventListener, true);
});

// 监听购买状态变化，实时更新权限
watch(
  () => buyStatus?.value,
  (newBuyStatus) => {
    if (newBuyStatus !== undefined) {
      // 当购买状态变化时，更新hasPermission
      hasPermission.value = newBuyStatus ? 1 : 0;
      // console.log('PrjManuWrapper: 购买状态已更新', newBuyStatus, '权限状态:', hasPermission.value);
    }
  },
  { immediate: true }
);

onMounted(async () => {
  // 优化：使用父组件提供的数据，避免重复调用API
  const projectIntroduceInfo = inject('projectIntroduceInfo', ref({}));
  const injectedHasPermission = inject('hasPermission', ref(0));

  if (projectIntroduceInfo.value && Object.keys(projectIntroduceInfo.value).length > 0) {
    // 使用父组件已获取的数据
    hasPermission.value = injectedHasPermission.value;
    prjType.value = projectIntroduceInfo.value.prjType;
  } else {
    const res = await getPrjIntroduceApi({
      spuId: route.query.spuId as string
    });
    hasPermission.value = res.data.hasPermission;
    prjType.value = res.data.prjType;
  }

  // 如果buyStatus已经注入且为true，优先使用buyStatus
  if (buyStatus?.value) {
    hasPermission.value = 1;
  }
});

// 章节信息
provide(
  'prjSectionInfo',
  computed<{ chapterId: string; prjId: string; contentId: string }>(() => ({
    chapterId: curChapterId.value ?? '',
    uniqueCode: uniqueCode.value ?? '',
    contentId: String(contentId.value ?? ''),
    prjId: prjId.value ?? ''
  }))
);
provide(
  'assessmentId',
  computed(() => assessmentId.value)
);

// 提供isMap状态给子组件
provide('isMap', isMap);

defineExpose({
  curChapterId: curChapterId,
  payDialogVisible,
  handleChangeSectionFn
});
</script>

<style scoped src="./css/PrjManuWrapper.less"></style>
