<template>
  <!-- 项目文稿 -->
  <div class="layout textfont">
    <!-- 主内容区域 -->
    <div class="main-content-area" :class="{ 'with-ksg-map': isMap }">
      <!-- ksgMap覆盖层 -->
      <div class="ksg-map-overlay" v-show="isMap">
        <KsgMap
          ref="ksgRef"
          :config="config"
          @load-more="handleLoadMore"
          :loading="loading"
          @click-label="handleClickLabel"
          class="ksgmap"
        />
        <!-- <KsgMap ref="ksgRef" :config="config" :loading="loading" /> -->
        <el-icon class="close-icon" size="2em" @click="closeMap">
          <Close />
        </el-icon>
      </div>

      <div
        class="content-card"
        v-if="!isMap && (prjForm != PrjForm.video || props.videoCaptionList)"
      >
        <el-watermark :font="font" :content="userInfo.username + '@' + userInfo.phone">
          <div
            class="content-text-wrapper hover-scrollbar"
            style="padding-bottom: 10px !important"
            :class="{ learning: $route.name == 'learning' }"
            ref="contentWrapperRef"
            id="script"
          >
            <ContentRenderer class="mystem" :content="contentWithOutlineIds" />
          </div>
        </el-watermark>
      </div>
    </div>

    <!-- 右侧大纲区域 -->
    <div
      class="outline-sidebar hover-scrollbar"
      v-if="prjForm != PrjForm.video"
      :class="{ 'has-drawer': showQuestionDrawer || showAnswerDrawer }"
    >
      <!-- 大纲内容 - 始终存在，作为背景层 -->
      <div class="outline-content">
        <MarkdownOutline v-if="outline.length > 0" :outline="outline" />
        <div v-else class="no-outline">
          <p>暂无目录大纲</p>
        </div>
      </div>

      <!-- 弹窗组件 - 绝对定位覆盖在大纲上方 -->
      <QuestionDrawer
        :visible="showQuestionDrawer"
        :selectedText="selectedText"
        :zIndex="componentZIndex.question"
        :buyStatus="props.buyStatus"
        @close="handleCloseQuestionDrawer"
        @showPayDialog="handleShowPayDialog"
      />
      <AnswerDrawerSidebar
        :visible="showAnswerDrawer"
        :questionData="currentQuestionData"
        :projectAuthor="projectAuthor"
        :zIndex="componentZIndex.answer"
        @close="handleCloseAnswerDrawer"
        @show-question="handleShowQuestionFromFloating"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// 处理显示PayDialog
const handleShowPayDialog = () => {
  console.log('PrjManuscript 到这里了');
  // 通过emit通知父组件的父组件打开PayDialog
  emits('update:payDialogVisible', true);
};

import { ref, onMounted, computed, watch, nextTick, type Events } from 'vue';
import { STATE_FLAG } from '@/types/learning';
import type { VideoCaptionListObj } from '@/types/learning';
import { PrjForm } from '@/types/project';
import { handleLineWord, getQuestionList, updataDom, markWord, unmarkWord } from '@/utils/lineWord';
import { useUserStore } from '@/stores/user';
import { getPrjIntroduceApi } from '@/apis/case';
import { BuyStatus } from '@/types/goods';
import { extractOutline, addIdsToHeadings, type OutlineItem } from '@/utils/outlineUtils';
import MarkdownOutline from './MarkdownOutline.vue';
import QuestionDrawer from '@/components/QuestionDrawer.vue';
import AnswerDrawerSidebar from '@/components/AnswerDrawerSidebar.vue';
import { Close } from '@element-plus/icons-vue';
import { useDrawerManager } from '@/composables/useDrawerManager';
// ksgMap相关导入
import { getAreaData, getFocusData, projectGraph } from '@/apis/ksgmap';
import { KsgMap } from '@endlessorigin/KsgMap'; //直接导入组件，局部使用方式
import { MODE, type Options } from '@endlessorigin/KsgMap';
const ksgRef = ref<any>(); //获取组件实例
let loading = ref<'loading' | 'loaded' | 'error'>('loading'); //loading状态
// 场景配置项
const config: Options = {
  model: MODE.MULTIPLE_ROOT, //Single_ROOT单根节点模,多根知识点场景(MODE.MULTIPLE_ROOT)，
  // 配置加载更多参数
  pointsLevelPager: {
    current: 1, //当前层级（从1层开始，默认第一层）
    levelSize: 2 //每次加载层数
  }
};

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

// const handleWord = inject('handleWord') as (e: Event) => void;

// 打包注释
// import { tr } from 'element-plus/es/locale';
// markWord()
interface Props {
  videoCaptionList?: [VideoCaptionListObj[]]; // 讲稿段落
  questionList?: any[]; // 问题列表
  renderContent?: any; // 文本项目文本
  // big?: false;
  isMap?: Boolean;
  buyStatus?: boolean;
}
const emits = defineEmits([
  'returnInit',
  'scrollInTop',
  'refresh',
  'refreshContent',
  'deleteQuestion',
  'search',
  'outline-change',
  'close-map',
  'update:payDialogVisible'
]);

const font = reactive({
  color: 'rgba(0, 0, 0, .07)'
});
// emit事件
const props = defineProps<Props>();
// const showContentWord=ref(false)

const prjForm = inject('prjForm') as Ref;
// const prjType = ref(1);
// const isBig = inject('isBig') as Ref;
// console.log('isBig', isBig);
// 移除模式依赖，改为同时支持阅读和划词模式
// const { mode } = storeToRefs(drawerControllerStore); // false为read模式 或者true为 question 模式

// 获取试学状态
const hasPermission = ref(0);

onMounted(async () => {
  try {
    // 优化：使用父组件提供的数据，避免重复调用API
    const injectedHasPermission = inject('hasPermission', ref(0));
    const injectedProjectAuthor = inject('projectAuthor', ref(''));

    if (injectedHasPermission.value !== undefined && injectedProjectAuthor.value) {
      // 使用父组件已获取的数据
      hasPermission.value = injectedHasPermission.value;
      projectAuthor.value = injectedProjectAuthor.value;
    } else {
      // 降级方案：如果父组件数据不可用，才调用API
      const res = await getPrjIntroduceApi({
        spuId: spuId
      });
      hasPermission.value = res.data.hasPermission;
      projectAuthor.value = res.data.editorName || '';
    }
  } catch (error) {
    console.error('获取试学状态失败', error);
  }
});
const qList = ref<any[]>([]);

const myWordContent = ref();
const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;

// 大纲相关
const outline = ref<OutlineItem[]>([]);

// 使用抽屉管理 composable
const {
  showQuestionDrawer,
  showAnswerDrawer,
  selectedText,
  currentQuestionData,
  componentZIndex,
  componentStack,
  projectAuthor,
  updateComponentLayer,
  handleShowQuestionDrawer,
  handleShowAnswerDrawer,
  handleCloseQuestionDrawer,
  handleCloseAnswerDrawer,
  handleShowQuestionFromFloating,
  initializeEventListeners,
  cleanupEventListeners
} = useDrawerManager();

// 抽屉管理相关逻辑已移至 useDrawerManager composable

// ksgMap相关变量
const isMap = inject('isMap') as Ref<boolean>;

// 监听isMap变化
watch(
  () => isMap?.value,
  async (newValue) => {
    // console.log('PrjManuscript - isMap changed to:', newValue);
    // 当知识图谱打开且有数据时，初始化知识图谱
    if (newValue && ksgMapData.value) {
      await init();
    }
  },
  { immediate: true }
);
const target = ref<HTMLElement | null>(null);

// 关闭地图
const closeMap = () => {
  // 通知父组件关闭地图
  emits('close-map');
};

// 计算属性：处理后的内容（包含大纲ID）
const contentWithOutlineIds = computed(() => {
  if (!myWordContent.value || prjForm.value === PrjForm.video) {
    return myWordContent.value || '';
  }
  // console.log('contentWithOutlineIds:', addIdsToHeadings(myWordContent.value));
  return addIdsToHeadings(myWordContent.value);
});

// 提取并更新大纲
const updateOutline = (content: string) => {
  if (!content) {
    outline.value = [];
    emits('outline-change', []);
    return;
  }
  const contentWithIds = addIdsToHeadings(content);
  // 提取大纲
  const newOutline = extractOutline(contentWithIds);
  outline.value = newOutline;
  // 通知父组件大纲变化
  emits('outline-change', newOutline);
  return contentWithIds;
};

watch(
  () => props.renderContent,
  async (newVal) => {
    myWordContent.value = newVal;
    // console.log('myWordContent', myWordContent.value);

    // 更新大纲
    if (newVal && prjForm.value !== PrjForm.video) {
      // console.log('[PrjManuscript] Calling updateOutline for draft content');
      updateOutline(newVal);
    } else {
      // console.log(
      //   '[PrjManuscript] Not calling updateOutline - newVal:',
      //   !!newVal,
      //   'prjForm:',
      //   prjForm.value,
      //   'isVideo:',
      //   prjForm.value === PrjForm.video
      // );
    }
    // console.log('renderMarkdown(myWordContent)', renderMarkdown(myWordContent.value));
  },
  { immediate: true }
);

const stateFlag = ref(STATE_FLAG.init);
const handleQuestionList = (id) => {
  getQuestionList(id).then((res) => {
    // questionDialogRef.value.showDialog(questionList, 2);
    qList.value = res;
    open.value = false;
  });
  // console.log('qList', qList.value);
};
// const isShowQuestionInBig = (id: number) => {
//   return (
//     isBig &&
//     filterQuestionList.value &&
//     filterQuestionList.value.length > 0 &&
//     paragraphId.value == id
//   );
// };

const contentWrapperRef = ref();
// 阅读时滑动滚
// 阅读模式，双向绑定，提问模式单项绑定。而且滚动条在最中间
const scrollElement = ref<HTMLElement>();
watch(
  () => scrollElement.value,
  (newVal, oldVal) => {
    if (newVal) {
      newVal.style.backgroundColor = '#a6d0ea';
      newVal.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    if (oldVal) {
      oldVal.style.backgroundColor = '';
    }
  }
);

const scrollBar = async (idx: number) => {
  const containerHeight = contentWrapperRef.value.offsetHeight;
  // 找到元素并且滑动
  const allParagraph = Array.from(document.querySelectorAll('.paragraph-wrapper')) as HTMLElement[];
  const activeParagraph = allParagraph?.[idx];
  sethighlightColor(idx);
  // 小屏可以使用这个api
  await nextTick();
  scrollElement.value = document.querySelector(`[oid="${curoid.value}"]`) as HTMLElement;
};
const activeIdx = ref(0);
// 设置高亮色
const sethighlightColor = (idx: number) => {
  activeIdx.value = idx;
};

const afterDeleteQuestion = (newcontent) => {
  myWordContent.value = newcontent;
  nextTick(() => {
    // if (!mode.value) {
    //   markWord();
    // } else {
    //   unmarkWord();
    // }
  });
};
const curoid = ref();
const hoverPList = (_idx: Number) => {
  curoid.value = _idx;
};

// ==============================
const open = ref(false);

// 监听章节变化来更新ksgMap数据
const prjSectionInfo = inject('prjSectionInfo') as Ref<{ chapterId: string }>;

// 存储知识图谱数据的响应式变量
const ksgMapData = ref<any>(null);

watch(
  () => prjSectionInfo?.value?.chapterId,
  async (newChapterId) => {
    if (newChapterId && spuId) {
      const pointsData = await projectGraph(spuId, newChapterId, 0, 10);
      console.log('pointsData:', pointsData);

      // 将数据存储到响应式变量中
      ksgMapData.value = pointsData;
      // data.value.topAreas = [];
      // data.value.points = pointsData;
      // ksgMap.value?.reloadData();
    }
  },
  { immediate: true }
);
const dataList = ref('');
const total = ref('');
const rootid = ref('');
async function init() {
  loading.value = 'loading'; //加载中状态

  //dataList - 后端响应的知识点数组
  //total - 总共多少个数据
  //root - 知识点id
  dataList.value = ksgMapData.value.records;
  console.log('dataList.value:', dataList.value);
  total.value = ksgMapData.value.total;
  rootid.value = 0;
  // console.log('datalist', dataList.value);
  // console.log('total', total.value);
  // console.log('rootid', rootid.value);

  ksgRef.value?.firstLoadPointsData(dataList.value, total.value, rootid.value); //当多根知识点模式，id为邻域id，方便后期数据请求查询
  loading.value = 'loaded'; //加载完成状态
}
onMounted(async () => {
  initializeEventListeners();
  // 初始化ksgMap
  target.value = document.getElementById('app')!;
});
const getContentWithOutlineIds = () => {
  if (!contentWithOutlineIds.value) {
    return [];
  }
  return contentWithOutlineIds.value;
};

defineExpose({
  scrollBar,
  sethighlightColor,
  handleQuestionList,
  afterDeleteQuestion,
  hoverPList,
  getContentWithOutlineIds,
  handleCloseQuestionDrawer,
  handleCloseAnswerDrawer
});

// 清理事件监听器
onUnmounted(() => {
  cleanupEventListeners();
});
</script>

<style scoped src="./css/PrjManuscript.less"></style>
