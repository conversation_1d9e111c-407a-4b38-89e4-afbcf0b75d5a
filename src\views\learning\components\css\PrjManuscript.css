.layout {
  height: 100%;
  display: flex;
  flex-direction: row;
  /* 改为水平布局 */
}
.main-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  /* 为ksgMap覆盖层提供定位基准 */
}
.main-content-area.with-ksg-map {
  /* 当显示ksgMap时的样式调整 */
  z-index: 1;
}
/* ksgMap覆盖层样式 */
.ksg-map-overlay {
  position: relative;
  width: 100%;
  height: calc(100vh - 130px);
  /* 保持与用户要求一致的高度 */
  z-index: 1000;
  border-radius: 5px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.ksg-map-overlay :deep(.ksgmap) {
  height: 100% !important;
  width: 100% !important;
  /* 限制最大尺寸，保持良好的用户体验 */
  z-index: 1000;
}
.ksg-map-overlay .close-icon {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
  color: #666;
  z-index: 1001;
  transition: color 0.2s ease;
}
.ksg-map-overlay .close-icon:hover {
  color: #409eff;
}
.outline-sidebar {
  position: relative;
  /* 为绝对定位的子组件提供定位上下文 */
  width: 300px;
  max-height: calc(100vh - 70px);
  /* 减去header和padding */
  background-color: white;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
  overflow-x: hidden;
  align-self: flex-start;
  /* 让侧边栏从顶部开始，根据内容高度自适应 */
  margin-top: 20px;
  /* 当有弹窗显示时，固定高度为最大高度 */
}
.outline-sidebar.has-drawer {
  height: calc(100vh - 70px);
}
.outline-sidebar .outline-content {
  padding: 0;
  box-sizing: border-box;
}
.outline-sidebar .outline-content .no-outline {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  padding: 20px;
  min-height: 100px;
  /* 给无内容状态一个最小高度 */
}
.mystem :deep(figure) {
  float: none;
}
.content-card {
  max-width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 允许flex子项收缩 */
  overflow: hidden;
  /* 确保滚动由子元素处理 */
}
.content-card .tool {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 37px;
}
.content-card .tool .mode {
  margin-left: 10px;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  display: flex;
  align-items: center;
}
.content-card .tool .mode :deep(.el-switch) {
  margin-right: 10px;
}
.content-card .tool .input {
  margin-top: 5px;
  margin-right: 10px;
  max-width: 230px;
}
.content-card .content-text-wrapper {
  width: 100%;
  flex: 1;
  overflow-y: auto !important;
  overflow-x: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
  padding: 0 10px;
  margin-top: 20px;
  position: relative;
  /* 为ContentRenderer组件添加样式 */
}
.content-card .content-text-wrapper :deep(#underline) {
  width: 100%;
  line-height: 1.6;
  /* 移除工具栏后调整最大高度 */
  max-height: calc(100vh - 120px);
}
.content-card:deep(.search-keys) {
  color: red;
  cursor: pointer;
  font-weight: 700;
}
.content-card:deep(.activeElement) {
  background-color: #ff9632;
}
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
  transform-origin: top;
  /* 设置动画起点为左侧 */
}
.scale-enter-from,
.scale-leave-to {
  transform: scaleY(0);
  /* 从左侧开始水平缩放 */
  opacity: 0;
}
/* 集成的代码块样式 - 从CodeBlock.vue移植 */
.code-container {
  margin: 16px 0;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  overflow: hidden;
  background: #f8f8f8;
}
.code-container .code-header {
  background: #dcdcdc;
  padding: 8px 12px;
  border-bottom: 1px solid #d4d4d4;
  font-size: 12px;
  color: #666;
}
.code-container .code-header .language-tag {
  font-weight: bold;
  text-transform: uppercase;
}
.code-container .code-content {
  margin: 0;
  padding: 16px;
  background: #f8f8f8;
  overflow-x: auto;
  font-family: 'Courier New', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}
.code-container .code-content code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
}
.code-container .code-content.has-collapse {
  transition: max-height 0.3s ease;
}
.code-container .collapse-control {
  display: flex;
  justify-content: center;
  padding: 8px;
  background: #dcdcdc;
  border-top: 1px solid #d4d4d4;
}
.code-container .collapse-control .collapse-btn {
  background: none;
  border: none;
  color: #000;
  cursor: pointer;
  padding: 4px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Microsoft YaHei', sans-serif;
  transition: color 0.2s;
}
.code-container .collapse-control .collapse-btn:hover {
  color: #808080;
}
/* 折叠状态 */
.code-container.collapsed .code-content {
  max-height: 200px;
  overflow: hidden;
}
/* 代码行号样式*/
.hljs {
  counter-reset: line;
}
.hljs .line::before {
  counter-increment: line;
  content: counter(line);
  display: inline-block;
  width: 2em;
  padding-right: 1em;
  color: #858585;
  text-align: right;
  user-select: none;
}
